// widgets/image_upload_widget.dart
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/widgets/PhotoView.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_controller.dart';
import 'package:ui_packages/ui_packages.dart';

class ImageUploadWidget extends StatelessWidget {
  final ValueChanged<List<XFile>>? onImagesChanged;

  ImageUploadWidget({super.key, this.onImagesChanged});
  final ImageUploadController controller = Get.put(ImageUploadController());
  @override
  Widget build(BuildContext context) {
    // 监听图片变化
    ever(controller.selectedImages, (List<XFile> images) {
      onImagesChanged?.call(images);
    });

    return Obx(() {
      // 计算需要显示的总项目数（图片 + 添加按钮）
      int totalItems = controller.selectedImages.length;
      if (controller.canUploadMore) {
        totalItems += 1;
      }

      return GridView.builder(
        shrinkWrap: true, //GridView 的大小会根据其内容动态调整，只占用内容所需的空间。
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2, // 每行两个 item
          crossAxisSpacing: 15.w,
          mainAxisSpacing: 15.w,
          childAspectRatio: 150 / 84, // 控制每个 item 的宽高比例
        ),
        itemCount: totalItems,
        padding: EdgeInsets.zero,
        itemBuilder: (context, position) {
          return Obx(() {
            return (position == controller.selectedImages.length &&
                    controller.canUploadMore)
                ? GestureDetector(
                    onTap: () {
                      showDateDialog(context);
                    },
                    child: WxAssets.images.logoSelectIcon.image(),
                  )
                : _buildImagePreview(controller, position);
          });
        },
      );
    });
  }

  void showDateDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      builder: (context) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24.r),
            color: Colours.color191921,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 38.w,
                height: 4,
                margin: EdgeInsets.only(top: 8.w),
                decoration: BoxDecoration(
                    color: Colours.color10D8D8D8,
                    borderRadius: BorderRadius.circular(4.r)),
              ),
              SizedBox(
                height: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      Get.back();
                      controller.pickImage(ImageSource.camera);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "拍照",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                      controller.pickImage(ImageSource.gallery);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1.w, color: Colours.color242424))),
                      alignment: Alignment.center,
                      child: Text(
                        "相册",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 52.w,
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      alignment: Alignment.center,
                      child: Text(
                        "取消",
                        style: TextStyles.regular.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(
                height: 47.w,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildImagePreview(ImageUploadController controller, int index) {
    return GestureDetector(
      onTap: () {
        Get.to(
          PhotoView(
            images: [],
            index: index,
            flag: 1,
          ),
        );
      },
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            height: 84.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.r),
              image: DecorationImage(
                image: FileImage(File(controller.selectedImages[index].path)),
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 4.w,
            right: 4.w,
            child: GestureDetector(
              onTap: () => controller.removeImage(index),
              child: Container(
                width: 20.w,
                height: 20.w,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 12.w,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton(ImageUploadController controller) {
    return GestureDetector(
      onTap: controller.showImageSourceDialog,
      child: WxAssets.images.logoSelectIcon.image(),
    );
  }

  void _showImagePreview(String imagePath) {
    Get.dialog(
      Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.contain,
                ),
              ),
            ),
            Positioned(
              top: 40.w,
              right: 20.w,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: const BoxDecoration(
                    color: Colors.black54,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24.w,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
