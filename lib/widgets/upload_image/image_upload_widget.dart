// widgets/image_upload_widget.dart
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shoot_z/widgets/upload_image/image_upload_controller.dart';

class ImageUploadWidget extends StatelessWidget {
  final ValueChanged<List<XFile>>? onImagesChanged;

  const ImageUploadWidget({super.key, this.onImagesChanged});

  @override
  Widget build(BuildContext context) {
    final ImageUploadController controller = Get.put(ImageUploadController());

    // 监听图片变化
    ever(controller.selectedImages, (List<XFile> images) {
      onImagesChanged?.call(images);
    });

    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 已选图片预览
            if (controller.selectedImages.isNotEmpty)
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.selectedImages.length,
                  itemBuilder: (context, index) {
                    return _buildImagePreview(controller, index);
                  },
                ),
              ),

            const SizedBox(height: 16),

            // 添加图片按钮
            if (controller.canUploadMore)
              GestureDetector(
                onTap: controller.showImageSourceDialog,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate,
                          size: 30, color: Colors.grey),
                      SizedBox(height: 4),
                      Text('添加图片',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ),
              ),

            // 提示文字
            const SizedBox(height: 8),
            Text(
              '已选 ${controller.selectedImages.length}/${controller.maxImages} 张图片',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ));
  }

  Widget _buildImagePreview(ImageUploadController controller, int index) {
    return Stack(
      children: [
        Container(
          width: 80,
          height: 80,
          margin: const EdgeInsets.only(right: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
              image: FileImage(File(controller.selectedImages[index].path)),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => controller.removeImage(index),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
