// controllers/image_upload_controller.dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ImageUploadController extends GetxController {
  final RxList<XFile> selectedImages = <XFile>[].obs;
  final int maxImages = 3;

  final ImagePicker _picker = ImagePicker();

  // 获取剩余可上传图片数量
  int get remainingSlots => maxImages - selectedImages.length;

  // 是否可以继续上传
  bool get canUploadMore => selectedImages.length < maxImages;

  // 选择图片
  Future<void> pickImage(ImageSource source) async {
    try {
      if (!canUploadMore) {
        Get.snackbar('提示', '最多只能上传$maxImages张图片');
        return;
      }

      final XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: 70,
        maxWidth: 1024,
        maxHeight: 1024,
      );

      if (image != null) {
        selectedImages.add(image);
      }
    } catch (e) {
      Get.snackbar('错误', '图片选择失败: ${e.toString()}');
    }
  }

  // 删除图片
  void removeImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
    }
  }

  // 清空所有图片
  void clearAllImages() {
    selectedImages.clear();
  }

  // 显示选择来源对话框
  void showImageSourceDialog() {
    Get.bottomSheet(
      SafeArea(
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () {
                Get.back();
                pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_camera),
              title: const Text('拍照'),
              onTap: () {
                Get.back();
                pickImage(ImageSource.camera);
              },
            ),
          ],
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
