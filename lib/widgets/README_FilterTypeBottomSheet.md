# FilterTypeBottomSheet 筛选类型底部弹窗

一个可复用的底部弹窗组件，用于显示筛选选项列表。

## 功能特性

- 🎨 统一的 UI 设计风格
- 📱 响应式布局，适配不同屏幕尺寸
- 🔧 高度可定制化
- 💫 流畅的动画效果
- 🛡️ 类型安全的回调处理

## 基础用法

```dart
import 'package:shoot_z/widgets/filter_type_bottom_sheet.dart';

// 准备筛选数据
final filterList = [
  {'title': '全场约战', 'id': '1'},
  {'title': '半场约战', 'id': '2'},
];

// 显示弹窗
FilterTypeBottomSheet.show(
  context,
  filterList: filterList,
  onItemSelected: (index) {
    final selectedItem = filterList[index];
    print('选择了: ${selectedItem["title"]}');
  },
);
```

## 高级用法

### 带返回值的弹窗

```dart
final result = await FilterTypeBottomSheet.show<String>(
  context,
  filterList: filterList,
  onItemSelected: (index) {
    // 处理选择逻辑
    return filterList[index]["id"];
  },
);

if (result != null) {
  print('用户选择了: $result');
}
```

### 复杂的选择处理

```dart
FilterTypeBottomSheet.show(
  context,
  filterList: filterList,
  onItemSelected: (index) {
    final selectedItem = filterList[index];
    
    // 根据选择执行不同的业务逻辑
    switch (selectedItem["id"]) {
      case '1':
        _applyFullCourtFilter();
        break;
      case '2':
        _applyHalfCourtFilter();
        break;
    }
    
    // 更新 UI 状态
    setState(() {
      selectedFilterId = selectedItem["id"];
      selectedFilterTitle = selectedItem["title"];
    });
  },
);
```

## 参数说明

### FilterTypeBottomSheet

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| filterList | `List<Map<String, dynamic>>` | ✅ | - | 筛选选项数据列表 |
| onItemSelected | `Function(int index)?` | ❌ | null | 选项被选中时的回调函数 |

### FilterTypeBottomSheet.show

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| context | `BuildContext` | ✅ | - | 构建上下文 |
| filterList | `List<Map<String, dynamic>>` | ✅ | - | 筛选选项数据列表 |
| onItemSelected | `Function(int index)?` | ❌ | null | 选项被选中时的回调函数 |

## 数据格式

筛选选项数据应该是一个包含 Map 的列表，每个 Map 至少包含 `title` 字段：

```dart
final filterList = [
  {
    'title': '显示文本',    // 必需：显示在弹窗中的文本
    'id': '唯一标识',      // 可选：用于业务逻辑的唯一标识
    'value': '自定义值',   // 可选：自定义数据
    // 可以添加更多自定义字段
  },
];
```

## 样式定制

组件使用了项目中的统一样式：

- 背景色: `Colours.color191921`
- 分割线: `Colours.color2F2F3B`
- 指示条: `Colours.color1AD8D8D8`
- 文字样式: `TextStyles.semiBold`

如需修改样式，请直接编辑 `FilterTypeBottomSheet` 组件源码。

## 注意事项

1. 确保 `filterList` 中的每个 Map 都包含 `title` 字段
2. `onItemSelected` 回调中的 `index` 参数对应 `filterList` 的索引
3. 弹窗会自动处理安全区域，无需额外处理
4. 组件依赖 `ui_packages` 和 `flutter_screenutil`，请确保已正确导入

## 迁移指南

如果你之前使用的是 `_showFilterTypeBottomSheet` 方法，可以按以下方式迁移：

### 旧用法
```dart
_showFilterTypeBottomSheet(context, filterList);
```

### 新用法
```dart
FilterTypeBottomSheet.show(
  context,
  filterList: filterList,
  onItemSelected: (index) {
    // 处理选择逻辑
  },
);
```
