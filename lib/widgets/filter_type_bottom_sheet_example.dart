import 'package:flutter/material.dart';
import 'package:shoot_z/widgets/filter_type_bottom_sheet.dart';

/// FilterTypeBottomSheet 使用示例
class FilterTypeBottomSheetExample extends StatelessWidget {
  const FilterTypeBottomSheetExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('筛选弹窗示例'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () => _showBasicExample(context),
              child: const Text('基础用法'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _showAdvancedExample(context),
              child: const Text('高级用法'),
            ),
          ],
        ),
      ),
    );
  }

  /// 基础用法示例
  void _showBasicExample(BuildContext context) {
    final filterList = [
      {'title': '全场约战', 'id': '1'},
      {'title': '半场约战', 'id': '2'},
    ];

    FilterTypeBottomSheet.show(
      context,
      filterList: filterList,
      onItemSelected: (index) {
        // 处理选择结果
        final selectedItem = filterList[index];
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('选择了: ${selectedItem["title"]}'),
          ),
        );
      },
    );
  }

  /// 高级用法示例
  void _showAdvancedExample(BuildContext context) {
    final filterList = [
      {'title': '全部', 'id': '0'},
      {'title': '免费', 'id': '1'},
      {'title': '收费', 'id': '2'},
      {'title': '官方创建', 'id': '3'},
    ];

    FilterTypeBottomSheet.show<String>(
      context,
      filterList: filterList,
      onItemSelected: (index) {
        final selectedItem = filterList[index];
        
        // 根据选择执行不同的逻辑
        switch (selectedItem["id"]) {
          case '0':
            _handleAllFilter();
            break;
          case '1':
            _handleFreeFilter();
            break;
          case '2':
            _handlePaidFilter();
            break;
          case '3':
            _handleOfficialFilter();
            break;
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('应用筛选: ${selectedItem["title"]}'),
          ),
        );
      },
    );
  }

  void _handleAllFilter() {
    // 处理"全部"筛选逻辑
    print('应用全部筛选');
  }

  void _handleFreeFilter() {
    // 处理"免费"筛选逻辑
    print('应用免费筛选');
  }

  void _handlePaidFilter() {
    // 处理"收费"筛选逻辑
    print('应用收费筛选');
  }

  void _handleOfficialFilter() {
    // 处理"官方创建"筛选逻辑
    print('应用官方创建筛选');
  }
}
