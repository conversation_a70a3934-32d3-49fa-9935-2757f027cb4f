import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_matches_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/event_bus.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:intl/intl.dart';
import 'package:shoot_z/utils/utils.dart';

class CreateArenaLogic extends GetxController with WidgetsBindingObserver {
  List siteType = [
    {'title': '室内', 'id': '0'},
    {'title': '室外', 'id': '1'}
  ];
  var siteTypeSelectIndex = 0.obs;
  List siteCategory = [
    {'title': '木地板', 'id': '0'},
    {'title': '塑胶', 'id': '1'},
    {'title': '悬浮拼接地板', 'id': '2'},
    {'title': '水泥地', 'id': '3'}
  ];
  List openTime = [
    {'title': '不对外开放', 'id': '0'},
    {'title': '全天开放', 'id': '1'},
    {'title': '白天开放', 'id': '2'},
    {'title': '晚上开放', 'id': '3'}
  ];
  List lightType = [
    {'title': '有灯光', 'id': '0'},
    {'title': '无灯光', 'id': '1'}
  ];
  List isFree = [
    {'title': '免费', 'id': '0'},
    {'title': '收费', 'id': '1'}
  ];
  var halfCourtPics = [].obs;
  bool isHalf = false;
  var challengeStrength = 1.obs; //约战强度：1新手局 2出汗局 3质量局 4强度局
  var challengeFormat = 1.obs; //约战赛制：1计时 2总分 3单节轮转 4其它
  var challengeCost = 1.obs; //约战费用：1AA 2其它
  var init = false.obs;
  var arenaList = <PlaceModel>[].obs;
  var selectArenaName = ''.obs;
  var selectArenaId = "".obs;
  // var selectArena = Rxn<PlaceModel>();
  var teamId = ''; //发起方球队ID
  MyBattleModel? editChallengeModel;
  TextEditingController addressController = TextEditingController(); //地点
  var searchText = ''.obs; //搜索框文本状态
  @override
  void onInit() {
    super.onInit();
    isHalf = (Get.arguments != null &&
        Get.arguments.containsKey('isHalf') &&
        Get.arguments['isHalf'] == true);
    if (Get.arguments != null && Get.arguments.containsKey('challengeModel')) {
      editChallengeModel = Get.arguments['challengeModel'];
      DateTime parsedDate = DateTime.parse(editChallengeModel?.matchTime ?? '');
      String formattedTime = DateFormat("HH:mm").format(parsedDate);
      String formattedDate = DateFormat("yyyy-MM-dd").format(parsedDate);
      isHalf = editChallengeModel?.challengeType == 2;

      addressController.text = editChallengeModel?.arenaName ?? '';
      challengeCost.value = editChallengeModel?.challengeCost ?? 1;
      challengeFormat.value = editChallengeModel?.challengeFormat ?? 1;
      challengeStrength.value = editChallengeModel?.challengeStrength ?? 1;
      teamId = editChallengeModel?.teamId ?? '';
      selectArenaName.value = editChallengeModel?.arenaName ?? '';
      selectArenaId.value = editChallengeModel?.arenaId ?? '';
      // selectArena.value =
      // remarksController.text = editChallengeModel?.remark ?? '';
    }
    requestPlace();
  }

  @override
  void onClose() {
    addressController.dispose();
    super.onClose();
  }

  Future<void> requestPlace() async {
    final position = LocationUtils.instance.position;
    if (position == null) {
      WxLoading.showToast(S.current.failed_location);
      return;
    }
    WxLoading.show();
    var map = {
      'latitude': '${position.latitude}',
      'longitude': '${position.longitude}'
    };
    var url = ApiUrl.vipRecommendList;

    final res = await Api().get(url, queryParameters: map);
    init.value = true;
    cc.log('mesaage${res.data}');
    if (res.isSuccessful()) {
      arenaList.value =
          (res.data as List).map((e) => PlaceModel.fromJson(e)).toList();
    } else {
      arenaList.value = [];
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }

  Future<void> createChallenge() async {
    if (addressController.text.isEmpty) {
      WxLoading.showToast('请选择约战地点');
      return;
    }

    WxLoading.show();
    var map = {
      'arenaId': selectArenaId.value,
      'challengeCost': challengeCost.value,
      'challengeFormat': challengeFormat.value,
      'challengeStrength': challengeStrength.value,
      'challengeType': isHalf ? 2 : 1, // 约战类型：1全场 2半场
      'teamId': teamId.isEmpty ? '0' : teamId,
    };
    cc.log('$map');
    var url = '';
    if (editChallengeModel != null) {
      url = ApiUrl.editChallenge;
      map['challengeId'] = editChallengeModel!.id ?? '';
    } else {
      url = ApiUrl.createChallenge;
    }
    final res = await Api().post(url, data: map);
    if (res.isSuccessful()) {
      if (editChallengeModel != null) {
        WxLoading.showToast('编辑成功');
      } else {
        WxLoading.showToast('发布成功');
      }
      //通知首页刷新数据
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.createSquadronBattle));
      AppPage.back(result: true);
    } else {
      WxLoading.showToast(res.message);
    }
    WxLoading.dismiss();
  }
}
