import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/site_list_page.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/stadium_list_page.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import '../../generated/l10n.dart';
import 'tab_venue_logic.dart';

class TabVenuePage extends StatefulWidget {
  const TabVenuePage({super.key});

  @override
  State<TabVenuePage> createState() => _HomePageState();
}

class _HomePageState extends State<TabVenuePage>
    with AutomaticKeepAliveClientMixin {
  final logic = Get.put(TabVenueLogic());
  final state = Get.find<TabVenueLogic>().state;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.list_of_stadiums),
        actions: [
          InkWell(
            onTap: () => AppPage.to(Routes.myArenaPage),
            child: Column(
              children: [
                SizedBox(
                  height: 8.w,
                ),
                WxAssets.images.mySiteIcon.image(),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  S.current.my_site,
                  style: TextStyles.display10,
                )
              ],
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          InkWell(
            child: Column(
              children: [
                SizedBox(
                  height: 8.w,
                ),
                WxAssets.images.stadiumMap.image(),
                SizedBox(
                  height: 8.w,
                ),
                Text(
                  S.current.stadium_map,
                  style: TextStyles.display10,
                )
              ],
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
        ],
      ),
      body: Obx(() =>
          Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(top: 15.w),
              child: TabBar(
                controller: logic.tabController,
                indicator: const UnderlineTabIndicator(
                  borderSide: BorderSide.none,
                  insets: EdgeInsets.zero,
                ),
                labelColor: Colors.transparent,
                unselectedLabelColor: Colors.transparent,
                labelStyle: TextStyles.titleSemiBold16,
                dividerColor: Colors.transparent,
                dividerHeight: 0,
                unselectedLabelStyle:
                    TextStyles.regular.copyWith(fontSize: 14.sp),
                overlayColor: WidgetStateProperty.all(Colors.transparent),
                isScrollable: true,
                tabAlignment: TabAlignment.center,
                tabs: List.generate(logic.tabNameList.length, (index) {
                  return Tab(
                    child: Container(
                      constraints: BoxConstraints(maxWidth: 70.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Obx(() => logic.tabbarIndex.value == index
                              ? ShaderMask(
                                  shaderCallback: (bounds) =>
                                      const LinearGradient(
                                    colors: [
                                      Colours.colorFFF9DC,
                                      Colours.colorE4C8FF,
                                      Colours.colorE5F3FF,
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ).createShader(bounds),
                                  child: Text(
                                    logic.tabNameList[index],
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    textAlign: TextAlign.center,
                                    style: TextStyles.titleSemiBold16.copyWith(
                                      color: Colors.white,
                                    ),
                                  ),
                                )
                              : Text(
                                  logic.tabNameList[index],
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.center,
                                  style: TextStyles.regular.copyWith(
                                    fontSize: 14.sp,
                                    color: Colours.color5C5C6E,
                                  ),
                                )),
                          SizedBox(height: 6.w),
                          logic.tabbarIndex.value == index
                              ? WxAssets.images.imgCheckIn2.image()
                              : SizedBox(height: 4.w),
                        ],
                      ),
                    ),
                  );
                }),
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: logic.tabController,
                children: [
                  StadiumListPage(),
                  SiteListPage(),
                ],
              ),
            ),
          ])),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
