import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/tab2Venue/place_list_item.dart';
import 'package:shoot_z/pages/tab3Create/court_footage/site_list_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/location_utils.dart';
import 'package:shoot_z/widgets/text_with_icon.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:shoot_z/widgets/filter_type_bottom_sheet.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';

class SiteListPage extends StatelessWidget {
  SiteListPage({super.key});

  final logic = Get.put(SiteListLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => Stack(
          children: [
            Column(
              children: [
                SizedBox(
                  height: 2.w,
                ),
                // 搜索框
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  height: 44.w,
                  alignment: Alignment.centerLeft,
                  decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(22.r),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      WxAssets.images.icSearch.image(),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                          child: TextField(
                        onTap: () => AppPage.to(Routes.placeSearch),
                        controller: logic.searchController,
                        keyboardType: TextInputType.text,
                        readOnly: true,
                        // onChanged: logic.onTextChanged,
                        decoration: InputDecoration(
                          // isDense: true,//isDense 为 true 会让 TextField 的高度变紧凑，同时调整光标和文本的位置。
                          contentPadding:
                              EdgeInsets.only(top: 0.w, bottom: 3.w),
                          border: InputBorder.none,
                          hintText: "请输入要查找的场地名称",
                          hintStyle: TextStyles.display14
                              .copyWith(color: Colours.color5C5C6E),
                        ),
                      )),
                      GestureDetector(
                          onTap: () {
                            logic.searchController.text = '';
                            logic.searchText.value = '';
                          },
                          child: Obx(() => Visibility(
                              visible: logic.searchText.value.isNotEmpty,
                              child: WxAssets.images.icSearchDelete.image()))),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                logic.init.value
                    ? Expanded(
                        child: NotificationListener(
                            onNotification: (ScrollNotification note) {
                              if (note.metrics.pixels ==
                                  note.metrics.maxScrollExtent) {
                                logic.loadMore();
                              }
                              return true;
                            },
                            child: RefreshIndicator(
                              onRefresh: logic.onRefresh,
                              child: Builder(builder: (context) {
                                return Obx(
                                  () => CustomScrollView(
                                    slivers: [
                                      if (logic.myArenaList.isNotEmpty)
                                        SliverToBoxAdapter(
                                          child: Container(
                                            alignment: Alignment.centerLeft,
                                            child: const TextWithIcon(
                                                title: '近期创作'),
                                          ).marginOnly(
                                              left: 15.w,
                                              bottom: 15.w,
                                              top: 15.w),
                                        ),
                                      LocationUtils
                                              .instance.havePermission.value
                                          ? _myArenalist()
                                          : _location(),
                                      if (logic.allArenaList.isNotEmpty)
                                        SliverToBoxAdapter(
                                            child: SingleChildScrollView(
                                          scrollDirection: Axis.horizontal,
                                          child: Row(
                                            children: [
                                              _createFilterBtn('全部', () {
                                                FilterTypeBottomSheet.show(
                                                  context,
                                                  filterList: logic.filterList1,
                                                  onItemSelected: (index) {
                                                    // 处理选择结果
                                                    print(
                                                        '选择了: ${logic.filterList1[index]["title"]}');
                                                  },
                                                );
                                              }),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              _createFilterBtn('全场', () {
                                                FilterTypeBottomSheet.show(
                                                  context,
                                                  filterList: logic.filterList2,
                                                  onItemSelected: (index) {
                                                    // 处理选择结果
                                                    print(
                                                        '选择了: ${logic.filterList2[index]["title"]}');
                                                  },
                                                );
                                              }),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              _createFilterBtn('官方创建', () {
                                                FilterTypeBottomSheet.show(
                                                  context,
                                                  filterList: logic.filterList3,
                                                  onItemSelected: (index) {
                                                    // 处理选择结果
                                                    print(
                                                        '选择了: ${logic.filterList3[index]["title"]}');
                                                  },
                                                );
                                              }),
                                              SizedBox(
                                                width: 10.w,
                                              ),
                                              _createFilterBtn('免费', () {
                                                FilterTypeBottomSheet.show(
                                                  context,
                                                  filterList: logic.filterList4,
                                                  onItemSelected: (index) {
                                                    // 处理选择结果
                                                    print(
                                                        '选择了: ${logic.filterList4[index]["title"]}');
                                                  },
                                                );
                                              }),
                                            ],
                                          ).marginOnly(
                                              left: 15.w,
                                              bottom: 15.w,
                                              right: 15.w),
                                        )),
                                      _allArenalist()
                                    ],
                                  ),
                                );
                              }),
                            )),
                      )
                    : buildLoad(),
              ],
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 8.w),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: EdgeInsets.only(bottom: 2.w),
                        width: 90.w,
                        height: 27.w,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image:
                                    WxAssets.images.pointBgArrowDown.provider(),
                                fit: BoxFit.fill)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          // crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            WxAssets.images.points2
                                .image(width: 16.w, height: 16.w),
                            SizedBox(
                              width: 3.w,
                            ),
                            Text(
                              '+500积分',
                              style: TextStyles.display12
                                  .copyWith(color: Colours.color7732ED),
                            )
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 8.w,
                      ),
                      Container(
                        width: double.infinity,
                        height: 50.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.symmetric(horizontal: 15.w),
                        decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(25.r)),
                        child: InkWell(
                          onTap: () {},
                          borderRadius: BorderRadius.circular(25.r),
                          child: Container(
                            width: double.infinity,
                            height: double.infinity,
                            alignment: Alignment.center,
                            child: Text(
                              '创建场地',
                              style: TextStyles.semiBold14,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _createFilterBtn(String title, VoidCallback onClick) {
    return InkWell(
      onTap: onClick,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        alignment: Alignment.center,
        height: 28.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14.r),
          border: Border.all(color: Colours.colorA8A8BC, width: 1.w),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: TextStyles.display12.copyWith(color: Colours.white),
            ),
            SizedBox(
              width: 6.w,
            ),
            WxAssets.images.arrowDownLinear.image()
          ],
        ),
      ),
    );
  }

  SliverToBoxAdapter _location() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          SizedBox(
            height: 71.w,
          ),
          WxAssets.images.icHomeLocationHint
              .image(width: 104.w, fit: BoxFit.fill),
          SizedBox(
            height: 30.w,
          ),
          Text(
            S.current.location_tips,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 30.w,
          ),
          WxButton(
            width: 125.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.w),
            backgroundColor: Colours.color22222D,
            text: S.current.open_now,
            textStyle: TextStyles.regular,
            onPressed: logic.openSettings,
          ),
        ],
      ),
    );
  }

  SliverList _myArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return PlaceListItem(
          model: logic.myArenaList[index],
        );
      },
      childCount: logic.myArenaList.isNotEmpty ? logic.myArenaList.length : 0,
    ));
  }

  SliverList _allArenalist() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return PlaceListItem(
          model: logic.allArenaList[index],
        );
      },
      childCount: logic.allArenaList.isNotEmpty ? logic.allArenaList.length : 0,
    ));
  }

  Widget allPlace() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colours.color2F2F3B, width: 1),
            ),
            child: GestureDetector(
              onTap: () => logic.toAll(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  WxAssets.images.icMoreAll.image(),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    S.current.all_arenas,
                    style: TextStyles.display12.copyWith(color: Colors.white),
                  )
                ],
              ),
            ),
          ),
        ));
  }
}
